﻿using Microsoft.EntityFrameworkCore;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Data.Repositories;
using MyAdaptiveCloud.Services.Exceptions;
using MyAdaptiveCloud.Services.Requests.Organizations;

namespace MyAdaptiveCloud.Services.Services
{
    public class WhiteLabelService : IWhiteLabelService
    {
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly IWhiteLabelRepository _whiteLabelRepository;
        private readonly IConfigurationService _configurationService;

        public WhiteLabelService(MyAdaptiveCloudContext context,
            IConfigurationService configurationService,
            IWhiteLabelRepository whiteLabelRepository)
        {
            _dbContext = context;
            _whiteLabelRepository = whiteLabelRepository;
            _configurationService = configurationService;
        }

        public async Task<WhiteLabel> GetDefaultWhitelabel()
        {
            WhiteLabel whiteLabel = null;

            var configuration = await _configurationService.GetWhiteLabelConfiguration();
            if (configuration == null)
            {
                return null;
            }

            var defaultWhiteLabelId = configuration.Default;

            if (!string.IsNullOrEmpty(defaultWhiteLabelId) && int.TryParse(defaultWhiteLabelId, out int id))
            {
                whiteLabel = await _dbContext.WhiteLabel.AsNoTracking().FirstOrDefaultAsync(row => row.WhiteLabelId == id);
            }

            return whiteLabel;
        }

        public async Task<WhiteLabel> GetWhiteLabel(int? organizationId, string domainName)
        {
            WhiteLabel whiteLabel = null;
            // Prioritize organization for white label
            if (organizationId.HasValue)
            {
                whiteLabel = _dbContext.WhitelabelByOrg(organizationId.Value);
            }
            // If no organizationId specified, use the domainName, if we have one
            else if (!string.IsNullOrEmpty(domainName))
            {
                whiteLabel = await _dbContext.WhiteLabel.FirstOrDefaultAsync(row => row.IsActive && row.DomainName == domainName);
            }

            if (whiteLabel == null)
            {
                whiteLabel = await GetDefaultWhitelabel();
            }

            // If we still don't have a white label (including default), return null
            if (whiteLabel == null)
            {
                return null;
            }

            return new WhiteLabel
            {
                AdaptiveCloudHostname = whiteLabel.AdaptiveCloudHostname,
                BannerLogo = whiteLabel.BannerLogo,
                DomainName = whiteLabel.DomainName,
                FavIcon = whiteLabel.FavIcon,
                IsActive = true,
                MainPortalLogo = whiteLabel.MainPortalLogo,
                NavigationLogo = whiteLabel.NavigationLogo,
                OrganizationId = whiteLabel.OrganizationId,
                PortalName = whiteLabel.PortalName,
                PrimaryColor = whiteLabel.PrimaryColor,
                SecondaryColor = whiteLabel.SecondaryColor,
                WhiteLabelId = whiteLabel.WhiteLabelId
            };
        }

        public async Task CreateWhiteLabel(int userId, int organizationId, EditWhiteLabelRequest request)
        {
            var whiteLabel = await GetByOrganization(organizationId);
            if (whiteLabel != null)
            {
                await UpdateWhiteLabel(userId, organizationId, whiteLabel.WhiteLabelId, request);
            }
            else
            {
                whiteLabel = new WhiteLabel
                {
                    DomainName = request.DomainName,
                    PortalName = request.PortalName,
                    MainPortalLogo = request.MainLogoId,
                    BannerLogo = request.HeaderLogoId,
                    FavIcon = request.FavIconId,
                    NavigationLogo = request.NavigationLogoId,
                    PrimaryColor = request.PrimaryColor,
                    SecondaryColor = request.SecondaryColor,
                    AdaptiveCloudHostname = request.AdaptiveCloudUrl,
                    CreatedBy = userId,
                    OrganizationId = organizationId,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow
                };

                _dbContext.WhiteLabel.Add(whiteLabel);
                await _dbContext.SaveChangesAsync();
            }
        }

        public async Task UpdateWhiteLabel(int userId, int organizationId, int whiteLabelId, EditWhiteLabelRequest request)
        {
            var whiteLabel = await _dbContext.WhiteLabel.FirstOrDefaultAsync(whiteLabel =>
                whiteLabel.OrganizationId == organizationId && whiteLabel.WhiteLabelId == whiteLabelId && whiteLabel.IsActive);

            if (whiteLabel == null)
            {
                return;
            }

            whiteLabel.AdaptiveCloudHostname = request.AdaptiveCloudUrl;
            whiteLabel.DomainName = request.DomainName;
            whiteLabel.PortalName = request.PortalName;
            whiteLabel.MainPortalLogo = request.MainLogoId;
            whiteLabel.BannerLogo = request.HeaderLogoId;
            whiteLabel.FavIcon = request.FavIconId;
            whiteLabel.NavigationLogo = request.NavigationLogoId;
            whiteLabel.PrimaryColor = request.PrimaryColor;
            whiteLabel.SecondaryColor = request.SecondaryColor;
            whiteLabel.UpdatedBy = userId;
            whiteLabel.OrganizationId = organizationId;
            whiteLabel.UpdatedDate = DateTime.UtcNow;

            await _dbContext.SaveChangesAsync();
        }


        public async Task<int?> GetImageIdFromWhiteLabel(ImageType imageType, int? organizationId, string domainName)
        {
            // First, try to get image from organization or domain-specific white label
            var whiteLabel = await GetWhiteLabel(organizationId, domainName);
            if (whiteLabel != null)
            {
                var imageId = GetImageIdByType(imageType, whiteLabel);
                if (imageId.HasValue)
                {
                    return imageId.Value;
                }
            }

            // If no organization/domain-specific image found, try the default white label
            try
            {
                var defaultWhiteLabel = await GetDefaultWhitelabel();
                if (defaultWhiteLabel != null)
                {
                    var defaultImageId = GetImageIdByType(imageType, defaultWhiteLabel);
                    if (defaultImageId.HasValue)
                    {
                        return defaultImageId.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception but don't throw - we want graceful fallback
                // You can add logging here if needed: _logger.LogWarning(ex, "Failed to get default white label for image type {ImageType}", imageType);
            }

            // Graceful fallback: return null if no image can be found
            // This allows the calling code to handle the missing image appropriately
            return null;
        }

        private int? GetImageIdByType(ImageType imageType, WhiteLabel whiteLabel)
        {
            if (whiteLabel == null)
            {
                return null;
            }

            int? imageId = null;
            switch (imageType)
            {
                case ImageType.portalLogo:
                    imageId = whiteLabel.MainPortalLogo;
                    break;
                case ImageType.navLogo:
                    imageId = whiteLabel.NavigationLogo;
                    break;
                case ImageType.favIcon:
                    imageId = whiteLabel.FavIcon;
                    break;
                case ImageType.headerLogo:
                    imageId = whiteLabel.BannerLogo;
                    break;
            }

            return imageId;
        }

        public async Task<WhiteLabel> GetByOrganization(int organizationId)
        {
            var whiteLabel = await _whiteLabelRepository.GetByOrganization(organizationId);
            return whiteLabel;
        }

        public async Task<bool> AnyByOrganization(int organizationId)
        {
            return await _whiteLabelRepository.AnyByOrganization(organizationId);
        }

        public async Task<WhiteLabel> GetByIdAndOrganization(int whiteLabelId, int organizationId, bool asNoTracking = false)
        {
            var whiteLabel = await _whiteLabelRepository.GetByIdAndOrganization(whiteLabelId, organizationId, asNoTracking);

            if (whiteLabel == null)
            {
                throw new NotFoundException(whiteLabelId.ToString(), "White Label not found.");
            }

            return whiteLabel;
        }

        public async Task<int> CreateImage(byte[] imageArray, string fileName, string contentType)
        {
            int imageId = await _whiteLabelRepository.CreateImage(imageArray, fileName, contentType);
            return imageId;
        }

        public async Task<WhiteLabel> GetWhiteLabel(string domainName)
        {
            return await _dbContext.WhiteLabel
                .FirstOrDefaultAsync(wl => wl.IsActive && wl.DomainName.ToLower().Contains(domainName.ToLower()));
        }
    }
}