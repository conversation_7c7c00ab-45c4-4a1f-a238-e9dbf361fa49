using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Microsoft.Net.Http.Headers;
using MyAdaptiveCloud.Api.Authorization;
using MyAdaptiveCloud.Api.Authorization.UserActions;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Api.Requests.Organizations;
using MyAdaptiveCloud.Api.ViewModel;
using MyAdaptiveCloud.Api.ViewModel.Organization;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.DTOs.Organization;
using MyAdaptiveCloud.Services.DTOs.Person;
using MyAdaptiveCloud.Services.Services;
using System.ComponentModel.DataAnnotations;

namespace MyAdaptiveCloud.Api.Controllers
{
    public class OrganizationController : AuthenticatedControllerBase
    {
        private readonly IOrganizationService _organizationService;
        private readonly IWhiteLabelService _whiteLabelService;
        private readonly IAgentCommunicationService _agentCommunicationService;
        private readonly IPersonService _personService;

        public OrganizationController(
            IOrganizationService orgService,
            IIdentityService identityService,
            IWhiteLabelService whiteLabelService,
            IMapper mapper,
            IPersonService personService,
            IAgentCommunicationService agentCommunicationService)
            : base(identityService, mapper)
        {
            _organizationService = orgService;
            _whiteLabelService = whiteLabelService;
            _agentCommunicationService = agentCommunicationService;
            _personService = personService;
        }

        // A logged in user can ask for 'selectable' orgs        
        [HttpGet("selectable")]
        public async Task<ApiDataResult<List<SelectableOrganizationDTO>>> GetSelectableUserOrganizations()
        {
            var userId = _identityService.PersonIdFromPrincipal(User);
            var selectableOrganizations = _organizationService.GetSelectableOrganizationsByUser(userId);

            return await Task.FromResult(new ApiDataResult<List<SelectableOrganizationDTO>>
            {
                Data = selectableOrganizations
            });
        }

        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs, Perms.ViewDevices, Perms.ManageDevices)]
        [HttpGet("{organizationId}/children")]
        public async Task<ApiDataSetResult<List<OrganizationFolderTreeNodeDTO>>> GetChildrenOrganizationFolders([FromRoute] int organizationId)
        {
            var childrenOrganizations = await _organizationService.GetChildrenOrganizationFolders(organizationId);
            return new ApiDataSetResult<List<OrganizationFolderTreeNodeDTO>>(childrenOrganizations);
        }

        /// <summary>
        ///     Gets the list of organizations that are descendants of the specified organization, including the supplied organization.
        /// </summary>
        /// <param name="organizationId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs, Perms.ViewDevicesReports, Perms.ManageDevicesReports)]
        [HttpGet("{organizationId}/descendants")]
        public async Task<ApiDataSetResult<List<OrganizationViewModel>>> GetOrganizationDescendants([FromRoute] int organizationId, [FromQuery] OrganizationRequest request)
        {
            var descendantOrganizations = await _organizationService.GetDescendantOrganizations(organizationId,
                _mapper.Map<Services.Requests.Organizations.OrganizationRequest>(request));

            return new ApiDataSetResult<List<OrganizationViewModel>>
            {
                Data = _mapper.Map<List<OrganizationViewModel>>(descendantOrganizations.Item1),
                TotalCount = descendantOrganizations.Item2
            };
        }

        /// <summary>
        ///     Gets the list of organizations that are descendants of the specified organization, including the supplied organization.
        ///     The resultset implements  <see cref="Core.Common.UserActions.IUserAction" /> and it is intended to be consumed by the Administration / Organization UI component.
        /// </summary>
        /// <param name="organizationId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs)]
        [TypeFilter(typeof(UserActionResultAttribute<OrganizationListItemDTO>))]
        [HttpGet("{organizationId}/list")]
        public async Task<ActionResult<ApiDataSetResult<List<OrganizationListItemDTO>>>> GetOrganizationList([FromRoute] int organizationId,
            [FromQuery] OrganizationListRequest request)
        {
            var organizations = await _organizationService.GetOrganizationsList(organizationId,
                _mapper.Map<Services.Requests.Organizations.OrganizationListRequest>(request));

            return new ApiDataSetResult<List<OrganizationListItemDTO>>
            {
                Data = organizations.Item1,
                TotalCount = organizations.Item2
            };
        }

        /// <summary>
        ///     Returns the list of organizations with associated application data, matching the query parameters.
        ///     This endpoint is intended for API usage only (MYAC-1930).
        /// </summary>
        [RootOrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs)]
        [HttpGet]
        public async Task<ApiDataSetResult<List<OrganizationWithApplicationDTO>>> GetOrganizationsWithApplications([FromQuery] OrganizationWithApplicationListRequest request)
        {
            var (organizations, totalCount) =
                await _organizationService.GetOrganizationsWithApplications(_mapper.Map<Services.Requests.Organizations.OrganizationWithApplicationListRequest>(request));

            return new ApiDataSetResult<List<OrganizationWithApplicationDTO>>(organizations, totalCount);
        }

        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs)]
        [HttpPost("{organizationId}/reconcile")]
        public async Task<ActionResult<ApiResult>> ReconcileOrganizationInformation([FromRoute] int organizationId)
        {
            await _organizationService.ReconcileOrganizationInformation(organizationId);

            return new ApiResult
            {
                Message = "Organization Reconciled successfully."
            };
        }


        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs, Perms.ViewDevices, Perms.ManageDevices, IncludeInactiveOrganizations = true)]
        [HttpGet("{organizationId:int}")]
        public async Task<ActionResult<ApiDataResult<OrganizationViewModel>>> GetOrganization([FromRoute] int organizationId)
        {
            var organization = await _organizationService.GetAnyOrganizationById(organizationId, true);
            return new ApiDataResult<OrganizationViewModel>
            {
                Data = _mapper.Map<OrganizationDTO, OrganizationViewModel>(organization)
            };
        }

        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs, Perms.ViewDevices, Perms.ManageDevices)]
        [HttpGet("{organizationId:int}/folder")]
        public async Task<ActionResult<ApiDataResult<OrganizationFolderTreeNodeDTO>>> GetOrganizationFolder([FromRoute] int organizationId)
        {
            var organization = await _organizationService.GetOrganizationFolder(organizationId);
            if (organization == null)
            {
                return NotFound(new ApiResult
                {
                    Message = "Organization not found"
                });
            }

            return new ApiDataResult<OrganizationFolderTreeNodeDTO>
            {
                Data = organization
            };
        }

        [RootOrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs)]
        [HttpGet("byip/{ipAddress}")]
        public async Task<ActionResult<ApiDataResult<OrganizationViewModel>>> GetOrganizationByAgentIP([FromRoute] string ipAddress)
        {
            var organization = await _agentCommunicationService.GetOrganizationFromIPAddress(ipAddress);

            return new ApiDataResult<OrganizationViewModel>
            {
                Data = _mapper.Map<Organization, OrganizationViewModel>(organization)
            };
        }

        [OrgAuthorize(Perms.AddOrgs, Name = "parentOrganizationId", IncludeInactiveOrganizations = false)]
        [HttpPost("{parentOrganizationId:int}")]
        public async Task<ActionResult<ApiDataResult<int>>> CreateOrganization([FromRoute] int parentOrganizationId, [FromBody] CreateOrganizationRequest request)
        {
            var organizationId = await _organizationService.CreateOrganization(parentOrganizationId,
                _identityService.PersonIdFromPrincipal(User),
                _mapper.Map<Services.Requests.Organizations.CreateOrganizationRequest>(request));

            return CreatedAtAction(nameof(CreateOrganization), new ApiDataResult<int>
            {
                Message = "Organization created successfully.",
                Data = organizationId
            });
        }

        [OrgAuthorize(Perms.ModifyOrgs, Perms.ApproveOrganization, Perms.ApprovePartnerOrganization, IncludeInactiveOrganizations = true)]
        [HttpPut("{organizationId:int}")]
        public async Task<ActionResult<ApiResult>> UpdateOrganization([FromRoute] int organizationId, [FromBody] EditOrganizationRequest request)
        {
            await _organizationService.UpdateOrganization(organizationId,
                _identityService.PersonIdFromPrincipal(User),
                _mapper.Map<Services.Requests.Organizations.EditOrganizationRequest>(request));

            return new ApiResult
            {
                Message = "Organization updated successfully."
            };
        }

        [OrgAuthorize(Perms.DeleteOrgs)]
        [HttpDelete("{organizationId:int}")]
        public async Task<ActionResult<ApiResult>> DeleteOrganization([FromRoute] int organizationId)
        {
            var currentUserId = _identityService.PersonIdFromPrincipal(User);
            await _organizationService.DeleteOrganization(organizationId, currentUserId);

            return Ok(new ApiResult
            {
                Message = "Organization deleted successfully"
            });
        }

        // Approve and Decline Organization / Partners
        [OrgAuthorize(Perms.ApproveOrganization, IncludeInactiveOrganizations = true)]
        [HttpPost("{organizationId:int}/approve")]
        public async Task<ActionResult<ApiResult>> ApproveOrganization([FromRoute] int organizationId)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(User);
            await _organizationService.ApproveOrganization(organizationId, false, currentUserId);

            return Ok(new ApiResult
            {
                Message = "Organization approved successfully."
            });
        }

        [OrgAuthorize(Perms.ApprovePartnerOrganization, IncludeInactiveOrganizations = true)]
        [HttpPost("{organizationId:int}/approvepartner")]
        public async Task<ActionResult<ApiResult>> ApprovePartnerOrganization([FromRoute] int organizationId)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(User);
            await _organizationService.ApproveOrganization(organizationId, true, currentUserId);

            return Ok(new ApiResult
            {
                Message = "Organization approved successfully."
            });
        }

        [OrgAuthorize(Perms.ApproveOrganization, IncludeInactiveOrganizations = true)]
        [HttpPost("{organizationId:int}/decline")]
        public async Task<ActionResult<ApiResult>> DeclineOrganization([FromRoute] int organizationId)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(User);
            await _organizationService.DeclineOrganization(organizationId, false, currentUserId);

            return Ok(new ApiResult
            {
                Message = "Organization declined successfully."
            });
        }

        [OrgAuthorize(Perms.ApprovePartnerOrganization, IncludeInactiveOrganizations = true)]
        [HttpPost("{organizationId:int}/declinepartner")]
        public async Task<ActionResult<ApiResult>> DeclinePartnerOrganization([FromRoute] int organizationId)
        {
            int currentUserId = _identityService.PersonIdFromPrincipal(User);
            await _organizationService.DeclineOrganization(organizationId, true, currentUserId);

            return Ok(new ApiResult
            {
                Message = "Organization declined successfully."
            });
        }

        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs)]
        [HttpGet("{organizationId:int}/isPartner")]
        public async Task<ActionResult<ApiDataResult<bool>>> GetOrganizationIsPartner([FromRoute] int organizationId)
        {
            return new ApiDataResult<bool>
            {
                Data = await _organizationService.IsPartnerOrganization(organizationId)
            };
        }

        [OrgAuthorize(Perms.ViewUsers, Perms.AddUsers, Perms.ModifyUsers, Perms.DeleteUsers)]
        [HttpGet("{organizationId:int}/user")]
        public async Task<ActionResult<ApiDataSetResult<List<PersonDTO>>>> GetOrganizationUsers([FromRoute] int organizationId, [FromQuery] string searchTerm)
        {
            var result = await _personService.GetOrganizationUsersForProvisioning(organizationId, searchTerm);
            return new ApiDataSetResult<List<PersonDTO>>
            {
                Data = result,
            };
        }

        [OrgAuthorize(Perms.ViewOrgs, IncludeInactiveOrganizations = true)]
        [HttpGet("{organizationId:int}/deleted/count")]
        public async Task<ActionResult<ApiDataResult<int>>> GetDeletedOrganizationsCount([FromRoute] int organizationId)
        {
            return new ApiDataResult<int>
            {
                Data = await _organizationService.GetDeletedOrganizationsCount(organizationId)
            };
        }

        [OrgAuthorize(Perms.ViewOrgs, IncludeInactiveOrganizations = true)]
        [TypeFilter(typeof(UserActionResultAttribute<DeletedOrganizationDTO>))]
        [HttpGet("{organizationId:int}/deleted")]
        public async Task<ActionResult<ApiDataSetResult<List<DeletedOrganizationDTO>>>> GetDeletedOrganizations([FromRoute] int organizationId,
            [FromQuery] DeletedOrganizationsRequest request)
        {
            var organizations = await _organizationService.GetDeletedOrganizations(organizationId,
                _mapper.Map<Services.Requests.Organizations.DeletedOrganizationsRequest>(request));

            return new ApiDataSetResult<List<DeletedOrganizationDTO>>
            {
                Data = organizations.Item1,
                TotalCount = organizations.Item2
            };
        }

        [OrgAuthorize(Perms.ModifyOrgs, IncludeInactiveOrganizations = true)]
        [HttpPost("{organizationId:int}/restore")]
        public async Task<ActionResult<ApiResult>> RestoreOrganization([FromRoute] int organizationId)
        {
            var currentUserId = _identityService.PersonIdFromPrincipal(User);
            await _organizationService.RestoreOrganization(organizationId, currentUserId);

            return new ApiResult
            {
                Message = "Organization restored successfully."
            };
        }


        #region WhiteLabel

        [OrgAuthorize(Perms.ViewOrgs, Perms.AddOrgs, Perms.ModifyOrgs, Perms.DeleteOrgs)]
        [HttpGet("{organizationId:int}/whitelabel")]
        public async Task<ActionResult<ApiDataResult<WhiteLabelViewModel>>> GetWhiteLabelByOrganization([FromRoute] int organizationId)
        {
            var whiteLabel = await _whiteLabelService.GetByOrganization(organizationId);

            if (whiteLabel == null)
            {
                return new ApiDataResult<WhiteLabelViewModel>
                {
                    Data = null
                };
            }

            var whiteLabelViewModel = new WhiteLabelViewModel
            {
                AdaptiveCloudUrl = whiteLabel.AdaptiveCloudHostname,
                HeaderLogoId = whiteLabel.BannerLogo,
                DomainName = whiteLabel.DomainName,
                FavIconId = whiteLabel.FavIcon,
                MainLogoId = whiteLabel.MainPortalLogo,
                NavigationLogoId = whiteLabel.NavigationLogo,
                PortalName = whiteLabel.PortalName,
                PrimaryColor = whiteLabel.PrimaryColor,
                SecondaryColor = whiteLabel.SecondaryColor,
                WhiteLabelId = whiteLabel.WhiteLabelId,
            };

            return new ApiDataResult<WhiteLabelViewModel>
            {
                Data = whiteLabelViewModel
            };
        }

        [OrgAuthorize(Perms.ModifyOrgs)]
        [HttpPost("{organizationId:int}/whitelabel")]
        public async Task<ActionResult<ApiResult>> CreateWhiteLabel([FromRoute] int organizationId, [FromBody] EditWhiteLabelRequest request)
        {
            var whiteLabelExist = await _whiteLabelService.AnyByOrganization(organizationId);
            if (whiteLabelExist)
            {
                return BadRequest(new ApiResult
                {
                    Message = "White Label already exists."
                });
            }

            var userId = _identityService.PersonIdFromPrincipal(User);
            await _whiteLabelService.CreateWhiteLabel(userId, organizationId,
                _mapper.Map<Services.Requests.Organizations.EditWhiteLabelRequest>(request));

            return CreatedAtAction(nameof(CreateWhiteLabel), new ApiResult
            {
                Message = "White Label created successfully."
            });
        }

        [OrgAuthorize(Perms.ModifyOrgs)]
        [HttpPut("{organizationId:int}/whitelabel/{whiteLabelId}")]
        public async Task<ActionResult<ApiResult>> UpdateWhiteLabel([FromRoute] int organizationId, [FromRoute] int whiteLabelId, [FromBody] EditWhiteLabelRequest request)
        {
            await _whiteLabelService.GetByIdAndOrganization(whiteLabelId, organizationId);

            var userId = _identityService.PersonIdFromPrincipal(User);
            await _whiteLabelService.UpdateWhiteLabel(userId, organizationId, whiteLabelId,
                _mapper.Map<Services.Requests.Organizations.EditWhiteLabelRequest>(request));

            return new ApiResult
            {
                Message = "White Label updated successfully."
            };
        }

        private async Task<ActionResult> GetImageUsingETag(int imageId)
        {
            var etag = HttpContext.Request.Headers[HeaderNames.IfNoneMatch];
            int cachedImageId = 0;
            if (etag != StringValues.Empty)
            {
                int.TryParse(etag, out cachedImageId);
            }

            if (imageId != cachedImageId)
            {
                Response.Headers.Append("ETag", imageId.ToString());
                var image = await _organizationService.GetImageById(imageId);
                return File(image.ImageBinary, image.ContentType);
            }
            else
            {
                return StatusCode(StatusCodes.Status304NotModified);
            }
        }

        private async Task<ActionResult> GetImageUsingETag(int? imageId)
        {
            // If no image ID is provided, return a transparent 1x1 pixel PNG
            if (!imageId.HasValue)
            {
                return GetTransparentPlaceholderImage();
            }

            return await GetImageUsingETag(imageId.Value);
        }

        private ActionResult GetTransparentPlaceholderImage()
        {
            // Return a transparent 1x1 pixel PNG image as a fallback
            // This prevents broken image icons in the UI
            var transparentPixel = Convert.FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
            return File(transparentPixel, "image/png");
        }

        [AllowAnonymous]
        [HttpGet("image/{imageType}")]
        public async Task<ActionResult> GetDefaultWhiteLabelImageByType([FromRoute][EnumDataType(typeof(ImageType))] ImageType imageType)
        {
            var imageId = await _whiteLabelService.GetImageIdFromWhiteLabel(imageType, null, HttpContext.Request.Host.Value);
            return await GetImageUsingETag(imageId);
        }


        [OrgAccessAuthorize(includeUnapprovedRoles: true)]
        [HttpGet("{organizationId:int}/image/{imageType}")]
        public async Task<ActionResult> GetImageByType([FromRoute] int organizationId, [FromRoute][EnumDataType(typeof(ImageType))] ImageType imageType)
        {
            var imageId = await _whiteLabelService.GetImageIdFromWhiteLabel(imageType, organizationId, string.Empty);
            return await GetImageUsingETag(imageId);
        }

        [HttpGet("image/{imageId:int}")]
        public async Task<ActionResult> GetImageById([FromRoute][Range(1, int.MaxValue)] int imageId)
        {
            return await GetImageUsingETag(imageId);
        }

        [OrgAuthorize(Perms.UpdateOrgImages)]
        [Consumes("multipart/form-data")]
        [HttpPost("{organizationId:int}/image")]
        public async Task<ActionResult<ApiDataResult<int>>> CreateImage([FromRoute] int organizationId, IFormFile file)
        {
            var allowedContentTypes = new string[] { "image/jpeg", "image/gif", "image/png", "image/jpg", "image/x-icon" };
            if (!allowedContentTypes.Contains(file.ContentType))
            {
                return BadRequest(new ApiResult
                {
                    Message = "Invalid image type"
                });
            }

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);

            var image = new Image
            {
                ImageBinary = stream.ToArray(),
                ImageName = file.FileName,
                ContentType = file.ContentType
            };

            var imageId = await _whiteLabelService.CreateImage(stream.ToArray(), file.FileName, file.ContentType);

            return CreatedAtAction(nameof(CreateImage), new ApiDataResult<int>
            {
                Data = imageId
            });
        }

        #endregion // White Label

        /// <summary>
        /// Returns the list of members that belong to the specified organization.
        /// </summary>
        [OrgAuthorize(Perms.ViewOrgs)]
        [HttpGet("{organizationId:int}/members")]
        public async Task<ActionResult<ApiDataResult<List<PersonDTO>>>> GetOrganizationMembers([FromRoute] int organizationId, [FromQuery] string searchTerm)
        {
            var organizationMembers = await _organizationService.GetOrganizationMembers(organizationId, searchTerm);
            return new ApiDataResult<List<PersonDTO>>(organizationMembers);
        }
    }
}