-- Fix for missing Title column in notification_type table
-- This script adds the Title column if it doesn't exist and populates it with appropriate values

-- Add the Title column (will be ignored if it already exists)
ALTER TABLE notification_type ADD COLUMN Title VARCHAR(200) DEFAULT '';

-- Update existing records with appropriate Title values
UPDATE notification_type SET Title='Severity 1', Description='Critical: Disruption of service' WHERE Id = 1;
UPDATE notification_type SET Title='Severity 2', Description='Urgent: Service degradation' WHERE Id = 2;
UPDATE notification_type SET Title='Severity 3', Description='Non-Urgent: Information, maintenance, or low-impact' WHERE Id = 3;
UPDATE notification_type SET Title='General', Description='Marketing Notification' WHERE Id = 4;

-- Verify the changes
SELECT Id, Title, Description, Priority
FROM notification_type
ORDER BY Id;
