-- Fix for missing default white label configuration
-- This script ensures both the configuration entry and default white label record exist

-- Step 1: Ensure WhiteLabel configuration category exists
INSERT IGNORE INTO `Configuration` (`Category`, `IsActive`) 
VALUES ('WhiteLabel', 1);

-- Step 2: Get the configuration ID for WhiteLabel category
SET @whiteLabelConfigId = (SELECT ConfigurationId FROM Configuration WHERE Category = 'WhiteLabel');

-- Step 3: Ensure the Default configuration value exists
INSERT IGNORE INTO `ConfigurationValues` (`Name`, `Value`, `IsSecret`, `ConfigurationId`, `InputType`) 
VALUES ('Default', '1', 0, @whiteLabelConfigId, 'input');

-- Step 4: Ensure default white label record exists (ID = 1)
INSERT IGNORE INTO `WhiteLabel` (`WhiteLabelId`, `OrganizationId`, `PortalName`, `DomainName`, `PrimaryColor`, `SecondaryColor`, `CreatedDate`, `IsActive`, `AdaptiveCloudHostname`) 
VALUES (1, 0, 'MyAdaptiveCloud', 'localhost', '#3079A7', '#666666', NOW(), 1, 'localhost');

-- Step 5: Verify the configuration
SELECT 'Configuration Check:' as Status;
SELECT c.Category, cv.Name, cv.Value 
FROM Configuration c 
JOIN ConfigurationValues cv ON c.ConfigurationId = cv.ConfigurationId 
WHERE c.Category = 'WhiteLabel';

SELECT 'Default WhiteLabel Check:' as Status;
SELECT WhiteLabelId, PortalName, DomainName, AdaptiveCloudHostname, IsActive 
FROM WhiteLabel 
WHERE WhiteLabelId = 1;
