-- Fix for missing Title column in notification_type table
-- This script adds the Title column if it doesn't exist and populates it with appropriate values

-- Check if Title column exists, if not add it
SET @column_exists = (
    SELECT COUNT(*)
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE
()
    AND TABLE_NAME = 'notification_type'
    AND COLUMN_NAME = 'Title'
);

-- Add the Title column if it doesn't exist
SET @sql =
IF(@column_exists = 0,
    'ALTER TABLE notification_type ADD COLUMN Title VARCHAR(200)',
    'SELECT "Title column already exists" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records with appropriate Title values
UPDATE notification_type SET Title='Severity 1', Description='Critical: Disruption of service' WHERE Id = 1;
UPDATE notification_type SET Title='Severity 2', Description='Urgent: Service degradation' WHERE Id = 2;
UPDATE notification_type SET Title='Severity 3', Description='Non-Urgent: Information, maintenance, or low-impact' WHERE Id = 3;
UPDATE notification_type SET Title='General', Description='Marketing Notification' WHERE Id = 4;

-- Make the column NOT NULL after all values are populated (only if it was just added)
SET @sql2 =
IF(@column_exists = 0,
    'ALTER TABLE notification_type MODIFY COLUMN Title VARCHAR(200) NOT NULL',
    'SELECT "Title column already exists, skipping NOT NULL constraint" as message'
);
PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- Verify the changes
SELECT Id, Title, Description, Priority
FROM notification_type
ORDER BY Priority;
