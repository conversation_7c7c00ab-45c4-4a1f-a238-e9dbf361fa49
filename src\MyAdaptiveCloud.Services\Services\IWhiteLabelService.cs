﻿using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Services.Requests.Organizations;

namespace MyAdaptiveCloud.Services.Services
{
    public interface IWhiteLabelService
    {
        Task<WhiteLabel> GetDefaultWhitelabel();

        Task<WhiteLabel> GetWhiteLabel(int? organizationId, string domainName);

        /// <summary>
        ///     Gets the id of the image to use for the specified image type.
        ///     It first tries to get the image id from the organization white label.
        ///     Then it falls back to the white label matching the domain name if supplied
        ///     Lastly, if none is found, it attempts to return the default white label image id.
        ///     Returns null if no image can be found (graceful fallback).
        /// </summary>
        /// <param name="imageType"></param>
        /// <param name="organizationId"></param>
        /// <param name="domainName"></param>
        /// <returns>Image ID if found, null if no image is available</returns>
        Task<int?> GetImageIdFromWhiteLabel(ImageType imageType, int? organizationId, string domainName);

        Task UpdateWhiteLabel(int userId, int organizationId, int whiteLabelId, EditWhiteLabelRequest request);

        Task CreateWhiteLabel(int userId, int organizationId, EditWhiteLabelRequest request);

        Task<WhiteLabel> GetByOrganization(int organizationId);

        Task<bool> AnyByOrganization(int organizationId);

        Task<WhiteLabel> GetByIdAndOrganization(int whiteLabelId, int organizationId, bool asNoTracking = false);

        Task<int> CreateImage(byte[] imageArray, string fileName, string contentType);

        Task<WhiteLabel> GetWhiteLabel(string domainName);
    }
}
