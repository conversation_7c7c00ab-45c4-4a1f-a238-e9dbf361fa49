# HTTP 500 Internal Server Error Fixes

## Issues Fixed

### Issue 1: ConfigurationService NullReferenceException
**Problem**: `GetWhiteLabelConfiguration()` method was calling `.Value` on null objects returned by `SingleOrDefault()` without null checking.

**Fix Applied**: Added null-conditional operator (`?.Value`) to prevent NullReferenceException:
- File: `src/MyAdaptiveCloud.Services/Services/ConfigurationService.cs`
- Line 573: Changed `configurationDtos.SingleOrDefault(v => v.Name == "Default").Value` to `configurationDtos.SingleOrDefault(v => v.Name == "Default")?.Value`

### Issue 2: WhiteLabelService Null Handling
**Problem**: Multiple methods in WhiteLabelService were not handling null values properly, causing downstream null reference exceptions.

**Fixes Applied**:
1. **GetDefaultWhitelabel()**: Added null check for configuration object
2. **GetWhiteLabel()**: Added null check before creating new WhiteLabel object
3. **GetImageIdFromWhiteLabel()**: Added comprehensive null checking with descriptive error messages
4. **GetImageIdByType()**: Added null check for whiteLabel parameter

**Files Modified**: `src/MyAdaptiveCloud.Services/Services/WhiteLabelService.cs`

### Issue 3: MySQL Title Column Missing
**Problem**: Entity Framework queries trying to select 'Title' column from notification_type table, but column doesn't exist in database.

**Root Cause**: Migration MYAC-1157.sql (which adds Title column to notification_type table) hasn't been applied to the production database.

**Solution**: Created manual migration script `fix_notification_type_title_column.sql` to add the missing column.

## Testing Instructions

### 1. Apply Database Migration (Critical - Do This First)
Run the following SQL script on the production database:
```sql
-- Add the Title column if it doesn't exist
ALTER TABLE notification_type ADD COLUMN IF NOT EXISTS Title VARCHAR(200) NOT NULL DEFAULT '';

-- Update existing records with appropriate Title values
UPDATE notification_type SET Title='Severity 1', Description='Critical: Disruption of service' WHERE Id = 1 AND (Title = '' OR Title IS NULL);
UPDATE notification_type SET Title='Severity 2', Description='Urgent: Service degradation' WHERE Id = 2 AND (Title = '' OR Title IS NULL);
UPDATE notification_type SET Title='Severity 3', Description='Non-Urgent: Information, maintenance, or low-impact' WHERE Id = 3 AND (Title = '' OR Title IS NULL);
UPDATE notification_type SET Title='General', Description='Marketing Notification' WHERE Id = 4 AND (Title = '' OR Title IS NULL);
```

### 2. Deploy Code Changes
Deploy the updated code files:
- `src/MyAdaptiveCloud.Services/Services/ConfigurationService.cs`
- `src/MyAdaptiveCloud.Services/Services/WhiteLabelService.cs`

### 3. Test API Endpoints
After deployment, test the following endpoints that were previously failing:

1. **Menu API**: `GET /api/menu/0`
   - Should return menu configuration without HTTP 500 error
   - Verify white label configuration is loaded properly

2. **Organization Image API**: `GET /api/organization/0/image/headerLogo`
   - Should return header logo image or appropriate response
   - Verify white label image resolution works

3. **Notification Types API**: `GET /api/notifications/{orgId}/types`
   - Should return notification types with Title property
   - Verify no MySQL column errors

### 4. Verify Application Login Flow
1. Navigate to application login page
2. Complete authentication process
3. Verify application loads properly after login
4. Check browser console for any remaining errors
5. Test navigation to different sections

### 5. Monitor Logs
Monitor application logs for:
- No more NullReferenceException in ConfigurationService
- No more "Unknown column 'n0.Title'" MySQL errors
- Successful white label configuration loading
- Proper image resolution for organizations

## Rollback Plan
If issues occur after deployment:
1. Revert code changes to previous version
2. The database migration is safe to keep (adds column with default values)
3. Monitor for any new issues

## Additional Notes
- The fixes follow the same null-safe pattern used in DataProtectionConfiguration
- Error messages in WhiteLabelService provide clear guidance for configuration issues
- The database migration is idempotent and safe to run multiple times
